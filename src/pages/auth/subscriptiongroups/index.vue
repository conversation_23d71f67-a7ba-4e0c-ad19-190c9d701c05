<template>
    <div>
        <adminheader :title="$t('navigations.subscriptiongroups')"
            :addFunction="isAdmin || isDev ? addSubscriptionGroup : ''" :reloadFunction="reload"></adminheader>

        <dtablesearch :searchFunc="searchFunc"></dtablesearch>
        <div v-if="databases == null">
            <dloading />
        </div>
        <template v-else>
            <dtable :columns="columns" :data="databases" columnColor="white">
                <template v-slot:action="slotProps">
                    <button :title="$t('c.edit')" @click="editRow(slotProps.item, slotProps.index)"
                        class="inline-block bg-blue-500 hover:bg-blue-700 text-white w-7 h-7 mr-2 rounded-full">
                        <svgicon icon="edit" dclass="w-4 h-4 m-1 inline-block" />
                    </button>
                    <button :title="$t('c.billing')" @click="billRow(slotProps.item, slotProps.index)"
                        class="inline-block bg-green-500 hover:bg-green-700 text-white w-7 h-7 mr-2 rounded-full">
                        <svgicon icon="cash" dclass="w-4 h-4 m-1 inline-block" />
                    </button>
                </template>
            </dtable>
            <dpagination :total="databases && databases.total || 0" :page="table.page" :limit="table.limit"
                :pageChange="pageChange" defaultColor="blue" />
        </template>
        <template v-if="item">
            <dform :item="item" :cancel="cancelNow" :token="token" :profile="profile" :save="saveFunc"></dform>
        </template>
        <template v-if="billItem">
            <billform :item="billItem" :cancelFunc="cancelBillItem"></billform>
        </template>
    </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import { crossStore } from '../../../store/cross-store'
import { subscriptionGroupStore } from '../../../store/subscriptiongroup-store'
import adminheader from '@/components/AdminHeader.vue'
import dtablesearch from '@/components/cvui/table/TableSearch.vue'
import dtable from '@/components/cvui/table/index.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import dloading from '@/components/cvui/loading.vue'
import dform from './form.vue'
import billform from './billform.vue'
import svgicon from '@/components/cvui/svgcollection.vue'
import { getUser, getUserById, getUserName } from '../../../api'

export default defineComponent({
    setup() {
        const authStore: any = inject("authStore")
        const authState = authStore.getState()
        const auth2State = auth2Store.getState()
        return {
            token: computed(() => authState.token),
            authStore: authStore,
            authState: authState,
            profile: computed(() => auth2State.profile),
            subscriptionGroupStore: subscriptionGroupStore,
            subscriptionGroupState: subscriptionGroupStore.getState()
        }
    },
    components: {
        adminheader,
        dtablesearch,
        dtable,
        dpagination,
        dloading,
        dform,
        billform,
        svgicon,
    },
    mounted() {
        this.reload()
    },
    data() {
        let item: any = null
        let billItem: any = null
        let deleteItem: any = null
        let itemStyle: any = {
            user: '',
            grpupcode: '',
            actiontype: '',
            status: true
        }
        let table: any = {
            limit: 10,
            page: 1,
            keywords: '',
            status: true,
        }
        let keywords: string = ''
        let userlist: any = []
        return {
            item,
            billItem,
            deleteItem,
            itemStyle,
            table,
            keywords,
            userlist
        }
    },
    methods: {
        getListChecker() {
            this.getUserList()
        },
        getUserList() {
            let useridlist2 = this.userlist.map((p: any) => p.id)
            let useridlist = []

            if (this.databases) {
                for (let i = 0; i < this.databases.data.length; i++) {
                    let data = this.databases.data[i]
                    if (data.customer) {
                        useridlist.push(data.customer)
                    }
                    if (data.agent) {
                        useridlist.push(data.agent)
                    }
                    if (data.user) {
                        useridlist.push(data.user)
                    }
                }
                useridlist = this.removeDuplicate(useridlist)
                useridlist = useridlist.filter((p: any) => useridlist2.indexOf(p) === -1)
                useridlist.filter(k => {
                    getUserName({ token: this.token, id: k }).then((rs: any) => {
                        this.userlist.push({
                            id: k,
                            value: rs.name || rs.email || k + ' (' + this.$t('c.noNameNoEmail') + ')'
                        })
                    })
                })
            }
        },
        removeDuplicate(arraylist: any) {
            return arraylist = [...new Set(arraylist)]
        },
        searchNow() {
            this.table.page = 1
            this.table.keywords = this.keywords
            this.loadDatabase()
        },
        searchFunc(p: string) {
            this.keywords = p
            this.searchNow()
        },
        reload() {
            this.table.page = 1
            this.table.keywords = ''
            this.keywords = ''
            this.loadDatabase()
        },
        loadDatabase() {
            let p = { ...this.table, token: this.token }
            this.subscriptionGroupStore.getSubscriptionGroups(p)
        },
        pageChange(p: any) {
            this.table.page = p
            this.loadDatabase()
        },
        initItem() {
            this.item = this.itemStyle
        },
        addSubscriptionGroup() {
            this.initItem()
        },
        cancelNow() {
            this.item = null
            this.reload()
        },
        cancelBillItem() {
            this.billItem = null
            this.reload()
        },
        editRow(item: any, index: any) {
            if (!this.item) {
                if (item.ID) { this.item = Object.assign({ id: item.ID }, item) }
                else { this.item = Object.assign({}, item) }

                this.item = JSON.parse(JSON.stringify(this.item))
            }
        },
        duplicateRow(p: any, i: any) {
            this.item = Object.assign({}, p)
            delete this.item.id
            delete this.item.updated_at
            delete this.item.created_at
        },
        billRow(item: any, index: any) {
            getUserById({ token: this.token, id: item.user }).then((res: any) => {
                this.billItem = res
            })
            // if (!this.billItem) {
            //     if (item.ID) { this.billItem = Object.assign({ id: item.ID }, item) }
            //     else { this.billItem = Object.assign({}, item) }

            //     this.billItem = JSON.parse(JSON.stringify(this.billItem))
            // }
        },
        saveFunc(p: any) {
            if (p.id) {
                this.subscriptionGroupStore.updateSubscriptionGroup({ form: p, id: p.id, token: this.token })
            } else {
                this.subscriptionGroupStore.createSubscriptionGroup({ form: p, token: this.token })
            }
            this.item = null
            this.billItem = null
            this.reload()
        },
    },
    computed: {
        columns() {
            let userlist = this.userlist
            return [
                { title: 'subscriptiongroups.user', key: 'user', type: 'string', objectlist: userlist, class: 'text-center' },
                { title: 'subscriptiongroups.groupcode', key: 'groupcode', type: 'string', class: 'text-center' },
                { title: 'subscriptiongroups.actiontype', key: 'actiontype', type: 'string', class: 'text-center' },
                { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
            ]
        },
        databases() {
            return subscriptionGroupStore.getState().subscriptionGroups
        },
        subscriptionGroupUpdate() {
            return subscriptionGroupStore.getState().subscriptionGroupUpdate
        },
        subscriptionGroupUpdateSuccess() {
            return subscriptionGroupStore.getState().subscriptionGroupUpdateSuccess
        },
        subscriptionGroupUpdateError() {
            return subscriptionGroupStore.getState().subscriptionGroupUpdateError
        },
        subscriptionGroupDeleteSuccess() {
            return subscriptionGroupStore.getState().subscriptionGroupDeleteSuccess
        },
        scopes() {
            let s: any = auth2Store.getState().profile

            return s && s.scopes
        },
        isAdmin() {
            let p: any = this.scopes
            if (p.includes('admin')) {
                return true;
            }
            return false;
        },
        isDev() {
            let p: any = this.scopes
            if (p.includes('dev')) {
                return true;
            }
            return false;
        },
    },
    watch: {
        subscriptionGroupUpdateSuccess(p) {
            if (p) {
                this.item = null
                crossStore.SetNotmsg({
                    title: this.$t('c.updateTitle') + this.$t('subscriptionGroups.subscriptionGroup'),
                    msg: this.$t('c.updateSuccess'),
                    type: 'success'
                })
            }
            this.getListChecker()
        },
        subscriptionGroupUpdateError(p) {
            if (p) {
                crossStore.SetModalmsg({
                    title: this.$t('c.updateTitle') + this.$t('subscriptionGroups.subscriptionGroup'),
                    msg: this.$t('c.updateError'),
                    type: 'error',
                    proceedTxt: this.$t('c.okay')
                })
            }
            this.getListChecker()
        },
        databases(p, o) {
            if (p && p != o) {
                this.getListChecker()
            }
        }
    },
})
</script>